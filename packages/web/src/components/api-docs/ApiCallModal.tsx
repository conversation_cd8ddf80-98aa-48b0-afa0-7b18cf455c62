import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>ack,
  Box,
  Text,
  Group,
  Button,
  Divider,
  <PERSON>ert,
  JsonInput,
  ScrollArea,
} from '@mantine/core'
import { IconPlayerPlay, IconAlertCircle } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { type MC } from '@/utils/modal'
import { ParameterForm } from './ParameterForm'

// 弹框选项类型
interface ApiCallModalOptions {
  procedure: {
    inputSchema: {
      type: string
      properties: Record<string, any>
      required?: string[]
      additionalProperties: boolean
      $schema: string
    }
    nodeType: 'procedure'
    procedureType: 'query' | 'mutation'
    pathFromRootRouter: string[]
    extraData: {
      description?: string
      parameterDescriptions: Record<string, string>
    }
  }
  name: string
  userToken?: string
  useRealToken: boolean
}

// 返回数据类型
interface ApiCallResult {
  success: boolean
  data?: any
  error?: string
}

// API调用弹框组件
export const ApiCallModal: MC<ApiCallModalOptions, ApiCallResult> = ({
  procedure,
  name,
  userToken,
  useRealToken,
  bind,
  finish,
  cancel,
}) => {
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [callResult, setCallResult] = useState<any>(null)
  const [callError, setCallError] = useState<string | null>(null)

  const path = procedure.pathFromRootRouter.join('.')
  const isQuery = procedure.procedureType === 'query'

  // 获取字段的默认值
  const getDefaultValue = (schema: any): any => {
    switch (schema.type) {
      case 'string':
        return ''
      case 'number':
        return 0
      case 'boolean':
        return false
      case 'array':
        return []
      case 'object':
        const obj: Record<string, any> = {}
        if (schema.properties) {
          Object.entries(schema.properties).forEach(([key, childSchema]: [string, any]) => {
            obj[key] = getDefaultValue(childSchema)
          })
        }
        return obj
      default:
        return null
    }
  }

  // 初始化表单数据
  const initializeFormData = () => {
    const initialData: Record<string, any> = {}

    if (procedure.inputSchema.properties) {
      Object.entries(procedure.inputSchema.properties).forEach(([key, schema]: [string, any]) => {
        initialData[key] = getDefaultValue(schema)
      })
    }

    setFormData(initialData)
  }

  // 调用API
  const handleApiCall = async () => {
    if (!userToken && useRealToken) {
      notifications.show({
        title: '错误',
        message: '请先生成API Token',
        color: 'red',
      })
      return
    }

    setIsLoading(true)
    setCallResult(null)
    setCallError(null)

    try {
      const token = useRealToken && userToken ? userToken : 'test-token'
      const url = `${window.location.origin}/api/trpc/${path}`

      let response: Response

      if (isQuery) {
        // GET请求
        const queryParams =
          Object.keys(formData).length > 0
            ? `?input=${encodeURIComponent(JSON.stringify(formData))}`
            : `?input=${encodeURIComponent('{}')}`

        response = await fetch(`${url}${queryParams}`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })
      } else {
        // POST请求
        response = await fetch(url, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        })
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      setCallResult(result)

      notifications.show({
        title: '调用成功',
        message: '接口调用完成',
        color: 'green',
      })

      // 调用成功后返回结果
      // finish({ success: true, data: result }, '接口调用成功')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setCallError(errorMessage)

      notifications.show({
        title: '调用失败',
        message: errorMessage,
        color: 'red',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 组件挂载时初始化表单数据
  useState(() => {
    initializeFormData()
  })

  return (
    <Modal
      opened={bind.visible}
      onClose={bind.onClose}
      title={`调用接口: ${procedure.extraData.description ?? name}`}
      size="lg"
      scrollAreaComponent={ScrollArea.Autosize}
    >
      <Stack gap="md">
        {/* 参数表单 */}
        {procedure.inputSchema.properties &&
        Object.keys(procedure.inputSchema.properties).length > 0 ? (
          <Box>
            <Text size="sm" fw={500} mb="md">
              请求参数：
            </Text>
            <Stack gap="md">
              {Object.entries(procedure.inputSchema.properties).map(
                ([key, schema]: [string, any]) => (
                  <ParameterForm
                    key={key}
                    schema={schema}
                    value={formData[key]}
                    onChange={value => setFormData(prev => ({ ...prev, [key]: value }))}
                    name={key}
                  />
                ),
              )}
            </Stack>
          </Box>
        ) : (
          <Alert icon={<IconAlertCircle size={16} />} color="blue">
            此接口无需参数
          </Alert>
        )}

        <Divider />

        {/* 调用按钮 */}
        <Group justify="flex-end">
          <Button variant="light" onClick={cancel}>
            取消
          </Button>
          <Button
            color="green"
            loading={isLoading}
            onClick={handleApiCall}
            leftSection={<IconPlayerPlay size={14} />}
          >
            发起调用
          </Button>
        </Group>

        {/* 调用结果 */}
        {callResult && (
          <Box>
            <Text size="sm" fw={500} mb="xs" c="green">
              调用成功：
            </Text>
            <JsonInput
              value={JSON.stringify(callResult, null, 2)}
              readOnly
              autosize
              minRows={5}
              maxRows={20}
            />
          </Box>
        )}

        {/* 错误信息 */}
        {callError && (
          <Alert icon={<IconAlertCircle size={16} />} color="red">
            <Text size="sm" fw={500} mb="xs">
              调用失败：
            </Text>
            <Text size="sm">{callError}</Text>
          </Alert>
        )}
      </Stack>
    </Modal>
  )
}
