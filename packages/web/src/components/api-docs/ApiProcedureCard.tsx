import {
  Paper,
  Text,
  Badge,
  Group,
  Box,
  Code,
  CopyButton,
  ActionIcon,
  Tooltip,
  Table,
  Stack,
  Button,
  Modal,
  JsonInput,
  TextInput,
  NumberInput,
  Switch,
  Divider,
  Alert,
  ScrollArea,
} from '@mantine/core'
import {
  IconCopy,
  IconCheck,
  IconPlayerPlay,
  IconChevronDown,
  IconChevronRight,
  IconPlus,
  IconTrash,
  IconAlertCircle,
} from '@tabler/icons-react'
import { useDocsStore } from '@/store/docsStore'
import { useState } from 'react'
import { useDisclosure } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'

interface ApiProcedure {
  inputSchema: {
    type: string
    properties: Record<string, any>
    required?: string[]
    additionalProperties: boolean
    $schema: string
  }
  nodeType: 'procedure'
  procedureType: 'query' | 'mutation'
  pathFromRootRouter: string[]
  extraData: {
    description?: string
    parameterDescriptions: Record<string, string>
  }
}

interface ApiProcedureCardProps {
  name: string
  procedure: ApiProcedure
  userToken?: string
}

// 递归渲染参数的组件
interface ParameterRowProps {
  name: string
  schema: any
  required: boolean
  level?: number
  description?: string
}

function ParameterRow({ name, schema, required, level = 0, description }: ParameterRowProps) {
  const [expanded, setExpanded] = useState(level < 2)
  const hasChildren = schema.type === 'object' || schema.type === 'array'

  const getTypeDisplay = (schema: any): string => {
    if (schema.type === 'array') {
      if (schema.items) {
        return `${getTypeDisplay(schema.items)}[]`
      }
      return 'array'
    }
    return schema.type || 'unknown'
  }

  const renderChildren = () => {
    if (schema.type === 'object' && schema.properties) {
      return Object.entries(schema.properties).map(([childName, childSchema]: [string, any]) => (
        <ParameterRow
          key={childName}
          name={childName}
          schema={childSchema}
          required={schema.required?.includes(childName) || false}
          level={level + 1}
          description={childSchema.description}
        />
      ))
    }

    if (schema.type === 'array' && schema.items) {
      return (
        <ParameterRow
          key="[item]"
          name="[item]"
          schema={schema.items}
          required={true}
          level={level + 1}
          description={schema.items.description}
        />
      )
    }

    return null
  }

  return (
    <>
      <Table.Tr style={{ backgroundColor: level > 0 ? 'var(--mantine-color-gray-0)' : undefined }}>
        <Table.Td style={{ paddingLeft: `${level * 20 + 12}px` }}>
          <Group gap="xs" wrap="nowrap">
            {hasChildren && (
              <ActionIcon size="xs" variant="subtle" onClick={() => setExpanded(!expanded)}>
                {expanded ? <IconChevronDown size={12} /> : <IconChevronRight size={12} />}
              </ActionIcon>
            )}
            <Code>{name}</Code>
          </Group>
        </Table.Td>
        <Table.Td>
          <Badge
            size="xs"
            variant="light"
            color={
              schema.type === 'string'
                ? 'blue'
                : schema.type === 'number'
                  ? 'green'
                  : schema.type === 'boolean'
                    ? 'orange'
                    : 'gray'
            }
          >
            {getTypeDisplay(schema)}
          </Badge>
        </Table.Td>
        <Table.Td>
          {required ? (
            <Badge size="xs" color="red" variant="light">
              必填
            </Badge>
          ) : (
            <Badge size="xs" color="gray" variant="light">
              可选
            </Badge>
          )}
        </Table.Td>
        <Table.Td>
          <Text size="sm" c="dimmed">
            {description || schema.description || '-'}
          </Text>
        </Table.Td>
      </Table.Tr>
      {hasChildren && expanded && renderChildren()}
    </>
  )
}

// 参数表单组件
interface ParameterFormProps {
  schema: any
  value: any
  onChange: (value: any) => void
  name: string
  level?: number
}

function ParameterForm({ schema, value, onChange, name, level = 0 }: ParameterFormProps) {
  const handleArrayChange = (index: number, newValue: any) => {
    const newArray = [...(value || [])]
    newArray[index] = newValue
    onChange(newArray)
  }

  const addArrayItem = () => {
    const newArray = [...(value || [])]
    newArray.push(getDefaultValueForSchema(schema.items))
    onChange(newArray)
  }

  const removeArrayItem = (index: number) => {
    const newArray = [...(value || [])]
    newArray.splice(index, 1)
    onChange(newArray)
  }

  const handleObjectChange = (key: string, newValue: any) => {
    onChange({
      ...(value || {}),
      [key]: newValue,
    })
  }

  const getDefaultValueForSchema = (schema: any): any => {
    switch (schema.type) {
      case 'string':
        return ''
      case 'number':
        return 0
      case 'boolean':
        return false
      case 'array':
        return []
      case 'object':
        const obj: Record<string, any> = {}
        if (schema.properties) {
          Object.entries(schema.properties).forEach(([key, childSchema]: [string, any]) => {
            obj[key] = getDefaultValueForSchema(childSchema)
          })
        }
        return obj
      default:
        return null
    }
  }

  if (schema.type === 'string') {
    return (
      <TextInput
        label={name}
        value={value || ''}
        onChange={e => onChange(e.target.value)}
        description={schema.description}
        style={{ marginLeft: level * 20 }}
      />
    )
  }

  if (schema.type === 'number') {
    return (
      <NumberInput
        label={name}
        value={value || 0}
        onChange={val => onChange(val)}
        description={schema.description}
        style={{ marginLeft: level * 20 }}
      />
    )
  }

  if (schema.type === 'boolean') {
    return (
      <Switch
        label={name}
        checked={value || false}
        onChange={e => onChange(e.target.checked)}
        description={schema.description}
        style={{ marginLeft: level * 20 }}
      />
    )
  }

  if (schema.type === 'array') {
    return (
      <Box style={{ marginLeft: level * 20 }}>
        <Group justify="space-between" mb="xs">
          <Text size="sm" fw={500}>
            {name} (数组)
          </Text>
          <Button
            size="xs"
            variant="light"
            leftSection={<IconPlus size={14} />}
            onClick={addArrayItem}
          >
            添加项
          </Button>
        </Group>
        {schema.description && (
          <Text size="xs" c="dimmed" mb="sm">
            {schema.description}
          </Text>
        )}
        <Stack gap="sm">
          {(value || []).map((item: any, index: number) => (
            <Group key={index} align="flex-start" wrap="nowrap">
              <Box style={{ flex: 1 }}>
                <ParameterForm
                  schema={schema.items}
                  value={item}
                  onChange={newValue => handleArrayChange(index, newValue)}
                  name={`[${index}]`}
                  level={0}
                />
              </Box>
              <ActionIcon
                color="red"
                variant="light"
                onClick={() => removeArrayItem(index)}
                mt="xl"
              >
                <IconTrash size={14} />
              </ActionIcon>
            </Group>
          ))}
        </Stack>
      </Box>
    )
  }

  if (schema.type === 'object') {
    return (
      <Box style={{ marginLeft: level * 20 }}>
        <Text size="sm" fw={500} mb="xs">
          {name} (对象)
        </Text>
        {schema.description && (
          <Text size="xs" c="dimmed" mb="sm">
            {schema.description}
          </Text>
        )}
        <Stack gap="sm">
          {schema.properties &&
            Object.entries(schema.properties).map(([key, childSchema]: [string, any]) => (
              <ParameterForm
                key={key}
                schema={childSchema}
                value={value?.[key]}
                onChange={newValue => handleObjectChange(key, newValue)}
                name={key}
                level={0}
              />
            ))}
        </Stack>
      </Box>
    )
  }

  // 其他类型使用JSON输入
  return (
    <JsonInput
      label={name}
      value={JSON.stringify(value, null, 2)}
      onChange={val => {
        try {
          onChange(JSON.parse(val))
        } catch {
          // 忽略解析错误
        }
      }}
      description={schema.description}
      style={{ marginLeft: level * 20 }}
      autosize
      minRows={2}
    />
  )
}

export function ApiProcedureCard({ name, procedure, userToken }: ApiProcedureCardProps) {
  const { useRealToken } = useDocsStore()
  const path = procedure.pathFromRootRouter.join('.')
  const isQuery = procedure.procedureType === 'query'
  const method = isQuery ? 'GET' : 'POST'

  // 调用弹框状态
  const [callModalOpened, { open: openCallModal, close: closeCallModal }] = useDisclosure(false)
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [callResult, setCallResult] = useState<any>(null)
  const [callError, setCallError] = useState<string | null>(null)

  // 调用API
  const handleApiCall = async () => {
    if (!userToken && useRealToken) {
      notifications.show({
        title: '错误',
        message: '请先生成API Token',
        color: 'red',
      })
      return
    }

    setIsLoading(true)
    setCallResult(null)
    setCallError(null)

    try {
      const token = useRealToken && userToken ? userToken : 'test-token'
      const url = `${window.location.origin}/api/trpc/${path}`

      let response: Response

      if (isQuery) {
        // GET请求
        const queryParams =
          Object.keys(formData).length > 0
            ? `?input=${encodeURIComponent(JSON.stringify(formData))}`
            : `?input=${encodeURIComponent('{}')}`

        response = await fetch(`${url}${queryParams}`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })
      } else {
        // POST请求
        response = await fetch(url, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        })
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      setCallResult(result)

      notifications.show({
        title: '调用成功',
        message: '接口调用完成',
        color: 'green',
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setCallError(errorMessage)

      notifications.show({
        title: '调用失败',
        message: errorMessage,
        color: 'red',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 初始化表单数据
  const initializeFormData = () => {
    const initialData: Record<string, any> = {}

    if (procedure.inputSchema.properties) {
      Object.entries(procedure.inputSchema.properties).forEach(([key, schema]: [string, any]) => {
        initialData[key] = getDefaultValue(schema)
      })
    }

    setFormData(initialData)
  }

  // 获取字段的默认值
  const getDefaultValue = (schema: any): any => {
    switch (schema.type) {
      case 'string':
        return ''
      case 'number':
        return 0
      case 'boolean':
        return false
      case 'array':
        return []
      case 'object':
        const obj: Record<string, any> = {}
        if (schema.properties) {
          Object.entries(schema.properties).forEach(([key, childSchema]: [string, any]) => {
            obj[key] = getDefaultValue(childSchema)
          })
        }
        return obj
      default:
        return null
    }
  }

  const generateCurlCommand = () => {
    const hasParams =
      procedure.inputSchema.properties && Object.keys(procedure.inputSchema.properties).length > 0
    const token = useRealToken && userToken ? userToken : 'YOUR_TOKEN'

    if (isQuery) {
      // GET请求，参数放在URL中
      let queryParams = ''
      if (hasParams) {
        const exampleParams: Record<string, any> = {}
        Object.entries(procedure.inputSchema.properties).forEach(([key, value]) => {
          switch (value.type) {
            case 'string':
              exampleParams[key] = 'example_string'
              break
            case 'number':
              exampleParams[key] = 123
              break
            case 'boolean':
              exampleParams[key] = true
              break
            default:
              exampleParams[key] = 'example_value'
          }
        })
        queryParams = `?input=${encodeURIComponent(JSON.stringify(exampleParams))}`
      } else {
        queryParams = `?input=${encodeURIComponent('{}')}`
      }

      return `curl -X GET "${window.location.origin}/api/trpc/${path}${queryParams}" \\
  -H "Authorization: Bearer ${token}"`
    } else {
      // POST请求，参数直接放在body中
      let body = '{}'
      if (hasParams) {
        const exampleParams: Record<string, any> = {}
        Object.entries(procedure.inputSchema.properties).forEach(([key, value]) => {
          switch (value.type) {
            case 'string':
              exampleParams[key] = 'example_string'
              break
            case 'number':
              exampleParams[key] = 123
              break
            case 'boolean':
              exampleParams[key] = true
              break
            default:
              exampleParams[key] = 'example_value'
          }
        })
        body = JSON.stringify(exampleParams)
      }

      return `curl -X POST "${window.location.origin}/api/trpc/${path}" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${token}" \\
  -d '${body}'`
    }
  }

  const renderParametersTable = () => {
    if (
      !procedure.inputSchema.properties ||
      Object.keys(procedure.inputSchema.properties).length === 0
    ) {
      return (
        <Text size="sm" c="dimmed">
          无参数
        </Text>
      )
    }

    const rows = Object.entries(procedure.inputSchema.properties).map(
      ([key, value]: [string, any]) => (
        <ParameterRow
          key={key}
          name={key}
          schema={value}
          required={procedure.inputSchema.required?.includes(key) || false}
          description={procedure.extraData.parameterDescriptions?.[key] || value.description}
        />
      ),
    )

    return (
      <Table>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>参数名</Table.Th>
            <Table.Th>类型</Table.Th>
            <Table.Th>必填</Table.Th>
            <Table.Th>描述</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
    )
  }

  return (
    <Paper p="md" withBorder radius="md" mb="md">
      <Stack gap="md">
        {/* 接口标题和操作 */}
        <Group justify="space-between" align="flex-start">
          <Box>
            <Group gap="xs" mb="xs">
              <Badge size="sm" color={isQuery ? 'blue' : 'orange'}>
                {method}
              </Badge>
              <Text fw={500} size="lg">
                {procedure.extraData.description ?? name}
              </Text>
            </Group>
            <Code>
              <Text component="span" c="dimmed" size="xs">
                {location.origin}/api/trpc/
              </Text>
              <Text component="span" fw={700} size="xs">
                {path}
              </Text>
            </Code>
          </Box>
          <Group gap="xs">
            <Button
              variant="filled"
              color="green"
              leftSection={<IconPlayerPlay size={14} />}
              onClick={() => {
                initializeFormData()
                openCallModal()
              }}
            >
              调用接口
            </Button>
            <CopyButton value={generateCurlCommand()}>
              {({ copied, copy }) => (
                <Tooltip label={copied ? '已复制curl命令' : '复制curl命令'}>
                  <ActionIcon color={copied ? 'teal' : 'blue'} variant="light" onClick={copy}>
                    {copied ? <IconCheck size={14} /> : <IconCopy size={14} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
        </Group>

        {/* 请求方法说明 */}
        {/* <Group gap="xs">
          <Text size="sm" c="dimmed">
            请求方法：
          </Text>
          <Badge size="sm" color={isQuery ? 'blue' : 'orange'}>
            {method}
          </Badge>
          <Text size="xs" c="dimmed">
            {isQuery ? '(Query接口固定使用GET方法)' : '(Mutation接口固定使用POST方法)'}
          </Text>
        </Group> */}

        {/* 参数表格 */}
        <Box>
          <Text size="sm" fw={500} mb="xs">
            请求参数：
          </Text>
          {renderParametersTable()}
        </Box>
      </Stack>

      {/* API调用弹框 */}
      <Modal
        opened={callModalOpened}
        onClose={closeCallModal}
        title={`调用接口: ${procedure.extraData.description ?? name}`}
        size="lg"
        scrollAreaComponent={ScrollArea.Autosize}
      >
        <Stack gap="md">
          {/* 参数表单 */}
          {procedure.inputSchema.properties &&
          Object.keys(procedure.inputSchema.properties).length > 0 ? (
            <Box>
              <Text size="sm" fw={500} mb="md">
                请求参数：
              </Text>
              <Stack gap="md">
                {Object.entries(procedure.inputSchema.properties).map(
                  ([key, schema]: [string, any]) => (
                    <ParameterForm
                      key={key}
                      schema={schema}
                      value={formData[key]}
                      onChange={value => setFormData(prev => ({ ...prev, [key]: value }))}
                      name={key}
                    />
                  ),
                )}
              </Stack>
            </Box>
          ) : (
            <Alert icon={<IconAlertCircle size={16} />} color="blue">
              此接口无需参数
            </Alert>
          )}

          <Divider />

          {/* 调用按钮 */}
          <Group justify="flex-end">
            <Button variant="light" onClick={closeCallModal}>
              取消
            </Button>
            <Button
              color="green"
              loading={isLoading}
              onClick={handleApiCall}
              leftSection={<IconPlayerPlay size={14} />}
            >
              发起调用
            </Button>
          </Group>

          {/* 调用结果 */}
          {callResult && (
            <Box>
              <Text size="sm" fw={500} mb="xs" c="green">
                调用成功：
              </Text>
              <JsonInput
                value={JSON.stringify(callResult, null, 2)}
                readOnly
                autosize
                minRows={5}
                maxRows={20}
              />
            </Box>
          )}

          {/* 错误信息 */}
          {callError && (
            <Alert icon={<IconAlertCircle size={16} />} color="red">
              <Text size="sm" fw={500} mb="xs">
                调用失败：
              </Text>
              <Text size="sm">{callError}</Text>
            </Alert>
          )}
        </Stack>
      </Modal>
    </Paper>
  )
}
