import { Group, Switch, Tooltip } from '@mantine/core'
import { IconKey } from '@tabler/icons-react'
import { useDocsStore } from '@/store/docsStore'

interface TokenSettingsProps {
  hasToken: boolean
}

export function TokenSettings({ hasToken }: TokenSettingsProps) {
  const { useRealToken, setUseRealToken } = useDocsStore()

  // 如果用户没有token，不显示开关
  if (!hasToken) {
    return null
  }

  return (
    <Group gap="xs">
      {/* <IconKey size={16} /> */}
      <Tooltip label="开启后，curl命令中将使用您的真实Token，否则显示占位符">
        <Switch
          size="sm"
          label="在curl中使用真实Token"
          checked={useRealToken}
          onChange={event => setUseRealToken(event.currentTarget.checked)}
        />
      </Tooltip>
    </Group>
  )
}
