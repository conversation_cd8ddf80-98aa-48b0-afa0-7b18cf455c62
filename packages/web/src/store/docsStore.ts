import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface DocsState {
  // 是否在curl命令中使用真实token
  useRealToken: boolean
  // 设置是否使用真实token
  setUseRealToken: (useRealToken: boolean) => void
  // 重置所有设置
  resetSettings: () => void
}

export const useDocsStore = create<DocsState>()(
  persist(
    (set) => ({
      useRealToken: false,
      setUseRealToken: (useRealToken) => set({ useRealToken }),
      resetSettings: () => set({ useRealToken: false }),
    }),
    {
      name: 'api-docs-storage',
      // 使用sessionStorage而不是localStorage，因为这个设置通常是临时的
      storage: {
        getItem: (name) => {
          const value = sessionStorage.getItem(name)
          return value ? JSON.parse(value) : null
        },
        setItem: (name, value) => {
          sessionStorage.setItem(name, JSON.stringify(value))
        },
        removeItem: (name) => {
          sessionStorage.removeItem(name)
        },
      },
    },
  ),
)
