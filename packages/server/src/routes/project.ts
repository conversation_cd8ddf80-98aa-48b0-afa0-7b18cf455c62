import { z } from 'zod'
import {
  protectedProcedure,
  router,
  adminProcedure,
  projectOwnerProcedure,
  projectProcedure,
  projectWriteProcedure,
} from '../trpc'
import { prisma } from '../prisma'
import { TRPCError } from '@trpc/server'
import { GitService } from '../services/repository'
import { omit } from 'lodash-es'

/**
 * 获取角色权限级别
 * @param role 角色
 * @returns 权限级别（数字越大权限越高）
 */
const getRoleLevel = (role: string): number => {
  switch (role) {
    case 'OWNER':
      return 3
    case 'WRITE':
      return 2
    case 'READ':
      return 1
    default:
      return 0
  }
}

export const projectRouter = router({
  list: protectedProcedure
    .meta({ description: 'Get list of all projects with user access information' })
    .query(async ({ ctx }) => {
      const projects = await prisma.project.findMany({
        include: {
          files: true,
          repositoryInfo: true,
          members: { include: { user: { select: { username: true, chineseName: true } } } },
        },
      })
      return projects.map(i => ({
        ...i,
        canView: i.members.some(m => m.username === ctx.user.username),
      }))
    }),

  delete: projectOwnerProcedure
    .meta({ description: 'Delete a project (owner only)' })
    .mutation(async ({ input }) => {
      await prisma.project.delete({
        where: { id: input.projectId },
      })
    }),

  add: adminProcedure
    .meta({ description: 'Create a new project from Git repository (admin only)' })
    .input(
      z.object({
        repository: z.string().describe('Git repository name'),
        project: z.string().describe('Project/module name within the repository'),
      }),
    )
    .mutation(async ({ input, ctx: { user } }) => {
      const { repository, project } = input

      const start = Date.now()

      const gitService = new GitService(user)
      const repositoryInfo = await gitService.getRepositoryModules(repository)
      console.log(`getRepositoryModules: ${Date.now() - start}ms`)
      if (!repositoryInfo.includes(project))
        throw new TRPCError({ code: 'BAD_REQUEST', message: '资源不存在，请检查资源名称是否正确' })
      const currentModuleInfo = await prisma.project.findUnique({
        where: { repository_name: { repository, name: project } },
      })
      if (currentModuleInfo) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: '此项目已经存在，无法重新创建' })
      } else {
        console.log('创建 repository + module')
        const projectInfo = await prisma.project.create({
          data: {
            name: project,
            repositoryInfo: {
              connectOrCreate: {
                where: { name: repository },
                create: { name: repository },
              },
            },
            createdByUser: {
              connect: { username: user.username },
            },
            members: {
              create: { username: user.username, role: 'OWNER' },
            },
          },
        })
        await updateProjectFilesWithHistories({
          repository,
          project,
          projectId: projectInfo.id,
          creator: user,
        })
      }
    }),

  sync: projectWriteProcedure
    .meta({ description: 'Synchronize project files with Git repository' })
    .mutation(async ({ ctx: { project, user } }) => {
      await updateProjectFilesWithHistories({
        repository: project.repository,
        project: project.name,
        projectId: project.id,
        creator: user,
      })
    }),

  info: projectProcedure
    .meta({ description: 'Get detailed project information including files and members' })
    .query(async ({ ctx: { project } }) => {
      const files = await prisma.protoFile.findMany({
        where: { projectId: project.id },
        include: {
          histories: {
            orderBy: { updatedAt: 'desc' },
            select: { commitId: true, updatedBy: true, updatedAt: true },
            take: 1,
          },
        },
      })
      const members = await prisma.projectMember.findMany({
        where: { projectId: project.id },
      })
      return {
        ...project,
        files: files.map(f => ({
          ...omit(f, ['histories']),
          latestCommit: f.histories[0],
        })),
        members,
      }
    }),

  getMembers: projectProcedure
    .meta({ description: 'Get list of project members with their details' })
    .query(async ({ ctx: { project } }) => {
      const members = await prisma.projectMember.findMany({
        where: { projectId: project.id },
        include: {
          user: {
            select: {
              username: true,
              chineseName: true,
              deptId: true,
              deptName: true,
              staffId: true,
            },
          },
        },
      })
      return members
    }),

  // 添加项目成员（支持批量添加）
  addMember: projectOwnerProcedure
    .meta({ description: 'Add members to project with specified roles (batch operation)' })
    .input(
      z.object({
        projectId: z.number().describe('Project ID to add members to'),
        members: z
          .array(
            z.object({
              username: z.string().describe('Username of the member to add'),
              role: z.enum(['READ', 'WRITE', 'OWNER']).describe('Role to assign to the member'),
              chineseName: z
                .string()
                .nullable()
                .optional()
                .describe('Chinese name of the member (optional)'),
            }),
          )
          .min(1)
          .describe('Array of members to add'),
      }),
    )
    .mutation(async ({ input }) => {
      const { projectId, members } = input

      // 检查项目是否存在
      const project = await prisma.project.findUnique({
        where: { id: projectId },
      })

      if (!project) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '项目不存在' })
      }

      // 获取已存在的成员
      const existingMembers = await prisma.projectMember.findMany({
        where: { projectId },
      })

      // 处理每个成员
      const results = await Promise.all(
        members.map(async member => {
          const { username, role, chineseName } = member

          // 检查用户是否存在
          const user = await prisma.user.findUnique({
            where: { username },
          })

          // 如果用户不存在，则创建用户
          if (!user) {
            await prisma.user.create({
              data: {
                username,
                is_admin: false,
                chineseName: chineseName || null,
              },
            })
          }
          // 如果用户存在但没有中文名，且提供了中文名，则更新用户信息
          else if (!user.chineseName && chineseName) {
            await prisma.user.update({
              where: { username },
              data: { chineseName },
            })
          }

          // 检查成员是否已存在
          const existingMember = existingMembers.find(m => m.username === username)

          if (existingMember) {
            // 如果成员已存在，比较权限级别，保留更高的权限
            const currentRoleLevel = getRoleLevel(existingMember.role)
            const newRoleLevel = getRoleLevel(role)

            // 如果新角色权限更高，则更新
            if (newRoleLevel > currentRoleLevel) {
              return prisma.projectMember.update({
                where: { projectId_username: { projectId, username } },
                data: { role },
              })
            }

            // 否则保持不变
            return existingMember
          } else {
            // 如果成员不存在，则添加
            return prisma.projectMember.create({
              data: { projectId, username, role },
            })
          }
        }),
      )

      return { success: true, count: results.length }
    }),

  // 更新成员角色
  updateMemberRole: projectOwnerProcedure
    .meta({ description: "Update a project member's role" })
    .input(
      z.object({
        projectId: z.number().describe('Project ID'),
        username: z.string().describe('Username of the member to update'),
        role: z.enum(['READ', 'WRITE', 'OWNER']).describe('New role to assign'),
      }),
    )
    .mutation(async ({ input }) => {
      const { projectId, username, role } = input

      // 检查成员是否存在
      const member = await prisma.projectMember.findUnique({
        where: { projectId_username: { projectId, username } },
      })

      if (!member) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '成员不存在' })
      }

      // 更新成员角色
      await prisma.projectMember.update({
        where: { projectId_username: { projectId, username } },
        data: { role },
      })

      return { success: true }
    }),

  // 删除成员
  removeMember: projectOwnerProcedure
    .meta({ description: 'Remove a member from the project' })
    .input(
      z.object({
        projectId: z.number().describe('Project ID'),
        username: z.string().describe('Username of the member to remove'),
      }),
    )
    .mutation(async ({ input }) => {
      const { projectId, username } = input

      // 检查成员是否存在
      const member = await prisma.projectMember.findUnique({
        where: { projectId_username: { projectId, username } },
      })

      if (!member) {
        throw new TRPCError({ code: 'NOT_FOUND', message: '成员不存在' })
      }

      // 删除成员
      await prisma.projectMember.delete({
        where: { projectId_username: { projectId, username } },
      })

      return { success: true }
    }),

  // 更新项目信息
  updateInfo: projectOwnerProcedure
    .meta({ description: 'Update project title and description' })
    .input(
      z.object({
        title: z.string().optional().describe('Project title'),
        desc: z.string().optional().describe('Project description'),
      }),
    )
    .mutation(async ({ input }) => {
      const { projectId, title, desc } = input

      // 更新项目信息
      return await prisma.project.update({
        where: { id: projectId },
        data: { title, desc },
      })
    }),
})

/**
 * 更新项目文件和历史
 */
export const updateProjectFilesWithHistories = async (info: {
  repository: string
  project: string
  projectId: number
  creator: { username: string; gitAccessToken: string | null }
}) => {
  const startGetFiles = Date.now()
  const { repository, project, projectId, creator } = info
  const gitService = new GitService(creator)
  // 创建所有文件
  const files = await gitService.getRepositoryModuleFiles(repository, project)
  console.log('查询并创建文件', `${Date.now() - startGetFiles}ms`)
  const savedFiles = await prisma.protoFile.findMany({
    where: { repository, projectId },
  })
  const newFiles = files.filter(file => !savedFiles.find(f => f.name === file))
  if (newFiles.length > 0) {
    await prisma.protoFile.createMany({
      data: newFiles.map(file => ({
        name: file,
        repository,
        projectId,
        refreshAt: new Date(),
      })),
    })
  }

  const allFiles = await prisma.protoFile.findMany({
    where: { repository, projectId },
  })

  // 批量获取所有文件历史
  if (allFiles.length > 0) {
    const batchStart = Date.now()

    // 由于API限制每次最多20个文件，需要分批处理
    const batchSize = 20
    const batches = []

    for (let i = 0; i < allFiles.length; i += batchSize) {
      batches.push(allFiles.slice(i, i + batchSize))
    }

    // 处理每一批文件
    for (const [batchIndex, batchFilePaths] of batches.entries()) {
      console.log(`处理第${batchIndex + 1}批文件，共${batchFilePaths.length}个`)

      const batchHistories = await gitService.getRepositoryFileHistoryBatch({
        repositoryFullName: repository,
        module: project,
        files: allFiles.map(f => f.name),
      })

      // 处理每个文件的历史记录
      for (const fileHistory of batchHistories) {
        const file = allFiles.find(f => f.name === fileHistory.file)
        // console.log(`处理文件 ${file} ${JSON.stringify(fileHistory)}`)
        if (!file) continue

        // 获取已保存的历史记录
        const savedHistory = await prisma.protoHistory.findMany({
          where: { fileId: file.id },
          select: { commitId: true },
        })

        // 过滤出新的历史记录
        const newHistory = fileHistory.history.filter(
          i => !savedHistory.some(sh => sh.commitId === i.commitId),
        )

        console.log(
          `文件 ${file.name} 共${fileHistory.history.length}条历史，新增${newHistory.length}条`,
        )

        // 创建新的历史记录
        if (newHistory.length > 0) {
          await prisma.protoHistory.createMany({
            data: newHistory.map(h => ({
              fileId: file.id,
              commitId: h.commitId,
              raw: '',
              json: '',
              updatedBy: h.username,
              updatedAt: h.updatedAt,
            })),
          })
        }
        console.log(`更新文件 ${file.name} 的 refreshAt`)
        // 更新文件的refreshAt
        await prisma.protoFile.update({
          where: { id: file.id },
          data: { refreshAt: new Date() },
        })
      }

      const batchEnd = Date.now()
      console.log(`批量获取文件历史完成: ${batchEnd - batchStart}ms`)
    }
  }
}
