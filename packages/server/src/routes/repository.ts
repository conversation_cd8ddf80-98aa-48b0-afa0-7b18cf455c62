import { z } from 'zod'
import { router, adminProcedure } from '../trpc'
import { GitService } from '../services/repository'

export const repositoryRouter = router({
  // list: protectedProcedure.query(async () => {
  //   const repositories = await prisma.repository.findMany({
  //     include: { projects: true },
  //   })
  //   return repositories
  // }),

  // /** 按关键词搜索所有可用的仓库 */
  // searchAvailableRepositories: protectedProcedure
  //   .input(z.object({ keyword: z.string().min(1) }))
  //   .query(async ({ input, ctx }) => {
  //     const gitService = new GitService(ctx.user)
  //     return await gitService.searchProjectsInGroup('rick_proto', input.keyword)
  //   }),

  // 获取项目组下所有项目
  getAvailableRepositories: adminProcedure.query(async ({ ctx }) => {
    const gitService = new GitService(ctx.user)
    return await gitService.getProjectsInGroup('rick_proto')
  }),

  getRepositoryAvailableProjects: adminProcedure
    .input(z.object({ repository: z.string() }))
    .query(async ({ input, ctx }) => {
      const gitService = new GitService(ctx.user)
      const { repository } = input
      return await gitService.getRepositoryModules(repository)
    }),
})
