import { router } from '../trpc'
import { userRouter } from './user'
import { protoRouter } from './proto'
import { projectRouter } from './project'
import { templateRouter } from './template/'
import { repositoryRouter } from './repository'
import { interfaceMetadataRouter } from './interface-metadata'
import { tagRouter } from './tag.router'
import { pathRuleRouter } from './path-rule.router'

export const appRouter = router({
  user: userRouter,
  project: projectRouter,
  template: templateRouter,
  repository: repositoryRouter,
  proto: protoRouter,
  interfaceMetadata: interfaceMetadataRouter,
  tag: tagRouter,
  pathRule: pathRuleRouter,
})

export type AppRouter = typeof appRouter
